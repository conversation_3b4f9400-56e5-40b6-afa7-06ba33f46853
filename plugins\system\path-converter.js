import path from "node:path"
import fs from "node:fs"

export class PathConverter extends plugin {
  constructor() {
    super({
      name: "路径转换器",
      dsc: "Windows到Docker路径转换",
      event: "message",
      priority: 1000,
      rule: []
    })
    
    // 配置路径映射
    this.pathMapping = {
      // Windows路径前缀 -> Docker容器路径前缀
      "C:\\Users\\<USER>\\anyulapp\\Yunzai": "/root/Yunzai"
    }
  }

  /**
   * 转换Windows路径到Docker路径
   * @param {string} windowsPath Windows绝对路径
   * @returns {string} Docker容器路径
   */
  convertPath(windowsPath) {
    if (!windowsPath || typeof windowsPath !== 'string') return windowsPath
    
    // 标准化路径分隔符
    let normalizedPath = windowsPath.replace(/\\/g, '/')
    
    // 查找匹配的映射规则
    for (const [winPrefix, dockerPrefix] of Object.entries(this.pathMapping)) {
      const normalizedWinPrefix = winPrefix.replace(/\\/g, '/')
      if (normalizedPath.startsWith(normalizedWinPrefix)) {
        const relativePath = normalizedPath.substring(normalizedWinPrefix.length)
        const dockerPath = dockerPrefix + relativePath
        logger.debug(`[路径转换] ${windowsPath} -> ${dockerPath}`)
        return dockerPath
      }
    }
    
    return windowsPath
  }

  /**
   * 检查是否为Windows绝对路径
   * @param {string} filePath 文件路径
   * @returns {boolean}
   */
  isWindowsAbsolutePath(filePath) {
    return /^[A-Za-z]:\\/.test(filePath)
  }
}

// 创建插件实例
const pathConverter = new PathConverter()

// 重写Bot.Buffer方法 - 这是关键的拦截点
const originalBuffer = Bot.Buffer
Bot.Buffer = async function(data, opts = {}) {
  if (typeof data === 'string') {
    // 处理file://协议的路径
    if (data.startsWith('file://')) {
      const filePath = data.replace(/^file:\/\//, '')
      if (pathConverter.isWindowsAbsolutePath(filePath)) {
        const convertedPath = pathConverter.convertPath(filePath)
        data = `file://${convertedPath}`
        logger.debug(`[路径转换] file:// 协议路径转换: ${filePath} -> ${convertedPath}`)
      }
    }
    // 处理普通的Windows绝对路径
    else if (pathConverter.isWindowsAbsolutePath(data)) {
      data = pathConverter.convertPath(data)
      logger.debug(`[路径转换] 普通路径转换: ${data}`)
    }
  }

  return originalBuffer.call(this, data, opts)
}

// 重写Bot.fileToUrl方法
const originalFileToUrl = Bot.fileToUrl
Bot.fileToUrl = async function(file, opts = {}) {
  if (typeof file === 'object' && file.buffer && typeof file.buffer === 'string') {
    if (pathConverter.isWindowsAbsolutePath(file.buffer)) {
      file.buffer = pathConverter.convertPath(file.buffer)
    }
  } else if (typeof file === 'string' && pathConverter.isWindowsAbsolutePath(file)) {
    file = pathConverter.convertPath(file)
  }

  return originalFileToUrl.call(this, file, opts)
}

// 重写Bot.fileType方法
const originalFileType = Bot.fileType
Bot.fileType = async function(data, opts = {}) {
  if (data && typeof data.file === 'string') {
    // 处理file://协议的路径
    if (data.file.startsWith('file://')) {
      const filePath = data.file.replace(/^file:\/\//, '')
      if (pathConverter.isWindowsAbsolutePath(filePath)) {
        const convertedPath = pathConverter.convertPath(filePath)
        data.file = `file://${convertedPath}`
        logger.debug(`[路径转换] fileType file:// 协议路径转换: ${filePath} -> ${convertedPath}`)
      }
    }
    // 处理普通的Windows绝对路径
    else if (pathConverter.isWindowsAbsolutePath(data.file)) {
      data.file = pathConverter.convertPath(data.file)
      logger.debug(`[路径转换] fileType 普通路径转换: ${data.file}`)
    }
  }

  return originalFileType.call(this, data, opts)
}

// 重写适配器的makeFile方法
const adapters = ['OneBotv11', 'OPQBot', 'ComWeChat', 'GSUIDCore', 'Satori']

adapters.forEach(adapterName => {
  const adapter = Bot.adapter.find(a => a.name === adapterName)
  if (adapter && adapter.makeFile) {
    const originalMakeFile = adapter.makeFile
    adapter.makeFile = async function(file, opts = {}) {
      if (typeof file === 'string') {
        // 处理file://协议的路径
        if (file.startsWith('file://')) {
          const filePath = file.replace(/^file:\/\//, '')
          if (pathConverter.isWindowsAbsolutePath(filePath)) {
            const convertedPath = pathConverter.convertPath(filePath)
            file = `file://${convertedPath}`
            logger.debug(`[路径转换] makeFile file:// 协议路径转换: ${filePath} -> ${convertedPath}`)
          }
        }
        // 处理普通的Windows绝对路径
        else if (pathConverter.isWindowsAbsolutePath(file)) {
          file = pathConverter.convertPath(file)
          logger.debug(`[路径转换] makeFile 普通路径转换: ${file}`)
        }
      }
      return originalMakeFile.call(this, file, opts)
    }
  }
})

// 重写文件上传相关方法
const originalSendGroupFile = Bot.sendGroupFile
if (originalSendGroupFile) {
  Bot.sendGroupFile = function(bot_id, group_id, file, folder, name) {
    if (typeof file === 'string') {
      // 处理file://协议的路径
      if (file.startsWith('file://')) {
        const filePath = file.replace(/^file:\/\//, '')
        if (pathConverter.isWindowsAbsolutePath(filePath)) {
          const convertedPath = pathConverter.convertPath(filePath)
          file = `file://${convertedPath}`
          logger.debug(`[路径转换] sendGroupFile file:// 协议路径转换: ${filePath} -> ${convertedPath}`)
        }
      }
      // 处理普通的Windows绝对路径
      else if (pathConverter.isWindowsAbsolutePath(file)) {
        file = pathConverter.convertPath(file)
        logger.debug(`[路径转换] sendGroupFile 普通路径转换: ${file}`)
      }
    }
    return originalSendGroupFile.call(this, bot_id, group_id, file, folder, name)
  }
}

const originalSendFriendFile = Bot.sendFriendFile
if (originalSendFriendFile) {
  Bot.sendFriendFile = function(bot_id, user_id, file, name) {
    if (typeof file === 'string') {
      // 处理file://协议的路径
      if (file.startsWith('file://')) {
        const filePath = file.replace(/^file:\/\//, '')
        if (pathConverter.isWindowsAbsolutePath(filePath)) {
          const convertedPath = pathConverter.convertPath(filePath)
          file = `file://${convertedPath}`
          logger.debug(`[路径转换] sendFriendFile file:// 协议路径转换: ${filePath} -> ${convertedPath}`)
        }
      }
      // 处理普通的Windows绝对路径
      else if (pathConverter.isWindowsAbsolutePath(file)) {
        file = pathConverter.convertPath(file)
        logger.debug(`[路径转换] sendFriendFile 普通路径转换: ${file}`)
      }
    }
    return originalSendFriendFile.call(this, bot_id, user_id, file, name)
  }
}

logger.info("[路径转换器] 插件加载完成，已启用Windows到Docker路径转换")