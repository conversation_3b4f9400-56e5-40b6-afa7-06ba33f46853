import path from "node:path"
import fs from "node:fs"

export class PathConverter extends plugin {
  constructor() {
    super({
      name: "路径转换器",
      dsc: "Windows到Docker路径转换",
      event: "message",
      priority: 1000,
      rule: []
    })
    
    // 配置路径映射
    this.pathMapping = {
      // Windows路径前缀 -> Docker容器路径前缀
      "C:\\Users\\<USER>\\anyulapp\\Yunzai": "/root/Yunzai"
    }
  }

  /**
   * 转换Windows路径到Docker路径
   * @param {string} windowsPath Windows绝对路径或相对路径
   * @returns {string} Docker容器路径
   */
  convertPath(windowsPath) {
    if (!windowsPath || typeof windowsPath !== 'string') return windowsPath

    // 处理相对路径，转换为绝对路径
    let absolutePath = windowsPath
    if (windowsPath.startsWith('./') || windowsPath.startsWith('../')) {
      // 相对路径转换为绝对路径
      absolutePath = path.resolve(process.cwd(), windowsPath)
      logger.debug(`[路径转换] 相对路径转绝对路径: ${windowsPath} -> ${absolutePath}`)
    }

    // 标准化路径分隔符
    let normalizedPath = absolutePath.replace(/\\/g, '/')

    // 查找匹配的映射规则
    for (const [winPrefix, dockerPrefix] of Object.entries(this.pathMapping)) {
      const normalizedWinPrefix = winPrefix.replace(/\\/g, '/')
      if (normalizedPath.startsWith(normalizedWinPrefix)) {
        const relativePath = normalizedPath.substring(normalizedWinPrefix.length)
        const dockerPath = dockerPrefix + relativePath
        logger.info(`[路径转换] ${windowsPath} -> ${dockerPath}`)
        return dockerPath
      }
    }

    return windowsPath
  }

  /**
   * 检查是否需要进行路径转换
   * @param {string} filePath 文件路径
   * @returns {boolean}
   */
  needsPathConversion(filePath) {
    // Windows绝对路径
    if (/^[A-Za-z]:\\/.test(filePath)) return true

    // 相对路径（在Windows环境下，相对路径最终会解析为Windows绝对路径）
    if (filePath.startsWith('./') || filePath.startsWith('../')) {
      const absolutePath = path.resolve(process.cwd(), filePath)
      return /^[A-Za-z]:\\/.test(absolutePath)
    }

    return false
  }

  /**
   * 检查是否为Windows绝对路径（保持向后兼容）
   * @param {string} filePath 文件路径
   * @returns {boolean}
   */
  isWindowsAbsolutePath(filePath) {
    return this.needsPathConversion(filePath)
  }
}

// 创建插件实例
const pathConverter = new PathConverter()

// 重写Bot.Buffer方法 - 这是关键的拦截点
const originalBuffer = Bot.Buffer
Bot.Buffer = async function(data, opts = {}) {
  logger.debug(`[路径转换] Bot.Buffer 被调用，数据: ${data}`)

  if (typeof data === 'string') {
    // 处理file://协议的路径
    if (data.startsWith('file://')) {
      const filePath = data.replace(/^file:\/\//, '')
      if (pathConverter.needsPathConversion(filePath)) {
        const convertedPath = pathConverter.convertPath(filePath)
        data = `file://${convertedPath}`
        logger.info(`[路径转换] Bot.Buffer file:// 协议路径转换: ${filePath} -> ${convertedPath}`)
      }
    }
    // 处理普通路径（包括相对路径和Windows绝对路径）
    else if (pathConverter.needsPathConversion(data)) {
      const originalData = data
      data = pathConverter.convertPath(data)
      logger.info(`[路径转换] Bot.Buffer 路径转换: ${originalData} -> ${data}`)
    }
  }

  return originalBuffer.call(this, data, opts)
}

// 重写Bot.fileToUrl方法
const originalFileToUrl = Bot.fileToUrl
Bot.fileToUrl = async function(file, opts = {}) {
  if (typeof file === 'object' && file.buffer && typeof file.buffer === 'string') {
    if (pathConverter.isWindowsAbsolutePath(file.buffer)) {
      file.buffer = pathConverter.convertPath(file.buffer)
    }
  } else if (typeof file === 'string' && pathConverter.isWindowsAbsolutePath(file)) {
    file = pathConverter.convertPath(file)
  }

  return originalFileToUrl.call(this, file, opts)
}

// 重写Bot.fileType方法
const originalFileType = Bot.fileType
Bot.fileType = async function(data, opts = {}) {
  if (data && typeof data.file === 'string') {
    // 处理file://协议的路径
    if (data.file.startsWith('file://')) {
      const filePath = data.file.replace(/^file:\/\//, '')
      if (pathConverter.isWindowsAbsolutePath(filePath)) {
        const convertedPath = pathConverter.convertPath(filePath)
        data.file = `file://${convertedPath}`
        logger.debug(`[路径转换] fileType file:// 协议路径转换: ${filePath} -> ${convertedPath}`)
      }
    }
    // 处理普通的Windows绝对路径
    else if (pathConverter.isWindowsAbsolutePath(data.file)) {
      data.file = pathConverter.convertPath(data.file)
      logger.debug(`[路径转换] fileType 普通路径转换: ${data.file}`)
    }
  }

  return originalFileType.call(this, data, opts)
}

// 延迟拦截适配器方法，确保适配器已经加载
setTimeout(() => {
  logger.debug('[路径转换] 开始拦截适配器方法...')

  // 重写适配器的makeFile方法
  const adapters = ['OneBotv11', 'OPQBot', 'ComWeChat', 'GSUIDCore', 'Satori']

  adapters.forEach(adapterName => {
    const adapter = Bot.adapter.find(a => a.name === adapterName)
    if (adapter && adapter.makeFile) {
      logger.debug(`[路径转换] 找到适配器 ${adapterName}，正在拦截 makeFile 方法`)
      const originalMakeFile = adapter.makeFile
      adapter.makeFile = async function(file, opts = {}) {
        logger.debug(`[路径转换] ${adapterName}.makeFile 被调用，文件: ${file}`)

        if (typeof file === 'string') {
          // 处理file://协议的路径
          if (file.startsWith('file://')) {
            const filePath = file.replace(/^file:\/\//, '')
            if (pathConverter.isWindowsAbsolutePath(filePath)) {
              const convertedPath = pathConverter.convertPath(filePath)
              file = `file://${convertedPath}`
              logger.info(`[路径转换] ${adapterName} makeFile file:// 协议路径转换: ${filePath} -> ${convertedPath}`)
            }
          }
          // 处理普通的Windows绝对路径
          else if (pathConverter.isWindowsAbsolutePath(file)) {
            const originalFile = file
            file = pathConverter.convertPath(file)
            logger.info(`[路径转换] ${adapterName} makeFile 普通路径转换: ${originalFile} -> ${file}`)
          }
        }
        return originalMakeFile.call(this, file, opts)
      }
    } else {
      logger.debug(`[路径转换] 适配器 ${adapterName} 未找到或没有 makeFile 方法`)
    }
  })

  logger.info('[路径转换] 适配器方法拦截完成')
}, 1000) // 延迟1秒确保适配器已加载

// 重写文件上传相关方法
const originalSendGroupFile = Bot.sendGroupFile
if (originalSendGroupFile) {
  Bot.sendGroupFile = function(bot_id, group_id, file, folder, name) {
    if (typeof file === 'string') {
      // 处理file://协议的路径
      if (file.startsWith('file://')) {
        const filePath = file.replace(/^file:\/\//, '')
        if (pathConverter.isWindowsAbsolutePath(filePath)) {
          const convertedPath = pathConverter.convertPath(filePath)
          file = `file://${convertedPath}`
          logger.debug(`[路径转换] sendGroupFile file:// 协议路径转换: ${filePath} -> ${convertedPath}`)
        }
      }
      // 处理普通的Windows绝对路径
      else if (pathConverter.isWindowsAbsolutePath(file)) {
        file = pathConverter.convertPath(file)
        logger.debug(`[路径转换] sendGroupFile 普通路径转换: ${file}`)
      }
    }
    return originalSendGroupFile.call(this, bot_id, group_id, file, folder, name)
  }
}

const originalSendFriendFile = Bot.sendFriendFile
if (originalSendFriendFile) {
  Bot.sendFriendFile = function(bot_id, user_id, file, name) {
    if (typeof file === 'string') {
      // 处理file://协议的路径
      if (file.startsWith('file://')) {
        const filePath = file.replace(/^file:\/\//, '')
        if (pathConverter.isWindowsAbsolutePath(filePath)) {
          const convertedPath = pathConverter.convertPath(filePath)
          file = `file://${convertedPath}`
          logger.debug(`[路径转换] sendFriendFile file:// 协议路径转换: ${filePath} -> ${convertedPath}`)
        }
      }
      // 处理普通的Windows绝对路径
      else if (pathConverter.isWindowsAbsolutePath(file)) {
        file = pathConverter.convertPath(file)
        logger.debug(`[路径转换] sendFriendFile 普通路径转换: ${file}`)
      }
    }
    return originalSendFriendFile.call(this, bot_id, user_id, file, name)
  }
}

// 添加消息发送前的拦截
setTimeout(() => {
  logger.debug('[路径转换] 开始拦截消息发送方法...')

  // 查找并拦截OneBotv11适配器的sendMsg方法
  const oneBotAdapter = Bot.adapter.find(a => a.name === 'OneBotv11')
  if (oneBotAdapter && oneBotAdapter.sendMsg) {
    logger.debug('[路径转换] 找到OneBotv11适配器，正在拦截sendMsg方法')
    const originalSendMsg = oneBotAdapter.sendMsg
    oneBotAdapter.sendMsg = async function(msg, send, sendForwardMsg) {
      logger.debug(`[路径转换] OneBotv11.sendMsg 被调用，消息: ${JSON.stringify(msg)}`)

      // 递归处理消息中的文件路径
      function processMessage(msgItem) {
        if (Array.isArray(msgItem)) {
          return msgItem.map(processMessage)
        }

        if (msgItem && typeof msgItem === 'object' && msgItem.data && msgItem.data.file) {
          const originalFile = msgItem.data.file
          let file = originalFile

          // 处理file://协议的路径
          if (typeof file === 'string' && file.startsWith('file://')) {
            const filePath = file.replace(/^file:\/\//, '')
            if (pathConverter.needsPathConversion(filePath)) {
              const convertedPath = pathConverter.convertPath(filePath)
              file = `file://${convertedPath}`
              logger.info(`[路径转换] sendMsg file:// 协议路径转换: ${filePath} -> ${convertedPath}`)
              msgItem.data.file = file
            }
          }
          // 处理普通路径（包括相对路径和Windows绝对路径）
          else if (typeof file === 'string' && pathConverter.needsPathConversion(file)) {
            file = pathConverter.convertPath(file)
            logger.info(`[路径转换] sendMsg 路径转换: ${originalFile} -> ${file}`)
            msgItem.data.file = file
          }
        }

        return msgItem
      }

      // 处理消息
      if (Array.isArray(msg)) {
        msg = msg.map(processMessage)
      } else {
        msg = processMessage(msg)
      }

      return originalSendMsg.call(this, msg, send, sendForwardMsg)
    }
  } else {
    logger.warn('[路径转换] 未找到OneBotv11适配器或sendMsg方法')
  }
}, 1500) // 延迟1.5秒确保适配器完全加载

logger.info("[路径转换器] 插件加载完成，已启用Windows到Docker路径转换")