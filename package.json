{"name": "trss-yunzai", "version": "3.1.3", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "description": "Bot", "main": "app.js", "type": "module", "scripts": {"app": "node .", "dev": "node . dev", "web": "node lib/tools/web.js", "start": "pm2 start config/pm2.yaml", "stop": "pm2 stop config/pm2.yaml", "restart": "pm2 restart config/pm2.yaml", "log": "pm2 log --lines 100", "lint": "git ls-files '*.js'|xargs prettier --write --list-different"}, "dependencies": {"art-template": "4.13.2", "chalk": "^5.6.2", "chokidar": "^4.0.3", "es-toolkit": "^1.39.10", "express": "^4.21.2", "file-type": "^21.0.0", "https-proxy-agent": "^7.0.6", "image-size": "^2.0.2", "level": "^10.0.0", "lodash": "link:lib/modules/lodash", "log4js": "^6.9.1", "md5": "link:lib/modules/md5", "moment": "^2.30.1", "node-fetch": "link:lib/modules/node-fetch", "node-schedule": "^2.1.1", "oicq": "link:lib/modules/oicq", "pm2": "^6.0.11", "puppeteer": "*", "redis": "^4.7.1", "sequelize": "^6.37.7", "sqlite3": "npm:@karinjs/sqlite3-cjs@0.1.0", "strip-ansi": "^7.1.2", "ulid": "^3.0.1", "ws": "^8.18.3", "yaml": "^2.8.1"}, "devDependencies": {"prettier": "^3.6.2"}, "imports": {"#miao": "./plugins/miao-plugin/components/index.js", "#miao.models": "./plugins/miao-plugin/models/index.js"}, "pnpm": {"onlyBuiltDependencies": ["@karinjs/sqlite3-cjs", "classic-level", "log4js", "puppeteer", "sharp"], "patchedDependencies": {"log4js": "lib/modules/log4js.patch", "streamroller": "lib/modules/streamroller.patch", "@karinjs/sqlite3-cjs": "lib/modules/sqlite3.patch"}}}