--- a/lib/appenders/stderr.js
+++ b/lib/appenders/stderr.js
@@ -1,3 +1,5 @@
+const rep = { "": "^N", "": "^O", "": "␇" }
+const reg = new RegExp(Object.keys(rep).join("|"), "g")
 function stderrAppender(layout, timezoneOffset) {
   return (loggingEvent) => {
-    process.stderr.write(`${layout(loggingEvent, timezoneOffset)}\n`);
+    process.stderr.write(`${layout(loggingEvent, timezoneOffset).replace(reg, i => rep[i])}\n`);
--- a/lib/appenders/stdout.js
+++ b/lib/appenders/stdout.js
@@ -1,3 +1,5 @@
+const rep = { "": "^N", "": "^O", "": "␇" }
+const reg = new RegExp(Object.keys(rep).join("|"), "g")
 function stdoutAppender(layout, timezoneOffset) {
   return (loggingEvent) => {
-    process.stdout.write(`${layout(loggingEvent, timezoneOffset)}\n`);
+    process.stdout.write(`${layout(loggingEvent, timezoneOffset).replace(reg, i => rep[i])}\n`);