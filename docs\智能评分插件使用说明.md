# TRSS Yunzai 智能评分插件使用说明

## 插件简介

智能评分插件是一个基于OCR技术的图片智能评分系统，专为TRSS Yunzai机器人开发。该插件可以识别用户发送的游戏装备图片，通过OCR技术提取词条信息，并根据指定的角色和情境进行智能评分。

## 功能特性

- 🔍 **OCR图片识别**：自动识别图片中的文字内容
- 🎯 **智能评分**：根据角色和情境计算装备评分
- 📋 **角色管理**：支持多个游戏角色的评分配置
- 🎬 **情境支持**：不同情境下的评分权重
- 📊 **详细报告**：提供词条详情和评分明细

## API配置

插件连接到智能评分API服务：
- **API地址**：https://pc.anyul.cn:5990/api/v1
- **账号**：admin
- **密码**：admin123

## 安装说明

1. 将 `智能评分.js` 文件放置在 `plugins/example/` 目录下
2. 确保已安装必要的依赖包：
   ```bash
   npm install node-fetch form-data
   ```
3. 重启Yunzai机器人

## 使用方法

### 1. 查看可用角色

发送指令：`#角色列表`

系统将返回所有可用的角色列表，包括角色名称和配置ID。

**示例输出：**
```
📋 可用角色列表：
1. 丹瑾 (ID: 3)
2. 今汐 (ID: 4)
3. 凌阳 (ID: 5)
...

💡 使用方法：发送图片并回复 "#智能评分 角色名 情境" 进行评分
```

### 2. 查看角色情境

发送指令：`#情境列表 角色名`

**示例：**
- `#情境列表 丹瑾`

系统将返回指定角色支持的所有情境。

**示例输出：**
```
📋 角色 "丹瑾" 的可用情境：
1. 4
2. 3
3. 1

💡 使用方法：发送图片并回复 "#ww评分 丹瑾 情境名" 进行评分
```

### 3. 图片评分

发送图片并回复：`#ww评分 角色名 情境名`

**示例：**
- `#ww评分 丹瑾 4`
- `#ww评分 今汐 3`

**支持的图片发送方式：**
1. 直接发送图片后回复评分指令
2. 引用包含图片的消息并发送评分指令

**示例输出：**
```
🎯 智能评分结果
━━━━━━━━━━━━━━━━━━━━
📊 总分：33.77 分
🎭 角色：卡提希娅-通用
🎬 情境：4
📈 有效词条：5 个
🎯 理论最高：78.99 分

📋 词条详情：
🔸 暴击伤害：44 (6.96分)
🔸 攻击：150 (0.00分)
🔹 暴击伤害：13.8 (8.74分)
🔹 暴击：6.9 (8.74分)
🔹 生命%：7.9 (5.50分)

❌ 无效词条：COST, +25.
━━━━━━━━━━━━━━━━━━━━
🔍 OCR置信度：96.5%
⏱️ 处理时间：0.69秒
```

## 指令列表

| 指令 | 功能 | 示例 |
|------|------|------|
| `#角色列表` | 查看所有可用角色 | `#角色列表` |
| `#情境列表 角色名` | 查看指定角色的情境 | `#情境列表 丹瑾` |
| `#ww评分 角色名 情境名` | 对图片进行评分 | `#ww评分 丹瑾 4` |

## 注意事项

1. **图片要求**：
   - 支持常见图片格式（JPG、PNG等）
   - 图片中的文字应清晰可读
   - 建议图片分辨率适中，过小或过大都可能影响识别效果

2. **角色和情境**：
   - 角色名称必须完全匹配（区分大小写）
   - 情境名称通常为数字（如：1、3、4等）
   - 使用前请先查看可用的角色和情境列表

3. **评分说明**：
   - 总分基于OCR识别的词条和配置的权重计算
   - 不同角色和情境下的评分标准不同
   - 无效词条不会计入总分

4. **错误处理**：
   - 如果角色或情境不存在，系统会提示相应错误
   - OCR识别失败时会返回错误信息
   - 网络连接问题会显示相应提示

## 技术特性

- **自动令牌管理**：插件会自动获取和刷新API访问令牌
- **错误重试**：网络请求失败时会进行适当的错误处理
- **缓存机制**：访问令牌会被缓存以提高性能
- **异步处理**：所有API请求都是异步处理，不会阻塞机器人

## 常见问题

**Q: 为什么提示"角色不存在"？**
A: 请使用 `#角色列表` 查看正确的角色名称，确保拼写完全正确。

**Q: 图片识别不准确怎么办？**
A: 请确保图片清晰，文字可读。可以尝试调整图片亮度或重新截图。

**Q: 评分结果为0分？**
A: 可能是OCR没有识别到有效词条，或者词条不在当前角色配置中。

**Q: 插件无响应？**
A: 检查网络连接和API服务状态，确保机器人有足够的权限。

## 更新日志

- **v1.0.0** (2024-01-01)
  - 初始版本发布
  - 支持基本的图片评分功能
  - 支持角色和情境查询
  - 集成OCR识别技术

---

*如有问题或建议，请联系开发者。*
