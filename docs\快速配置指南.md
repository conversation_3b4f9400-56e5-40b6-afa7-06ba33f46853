# Yunzai路径转换器快速配置指南

## 问题描述
您遇到的错误：
```
Error: ENOENT: no such file or directory, copyfile 'C:\Users\<USER>\anyulapp\Yunzai\data\rcmp4\881042075\temp.mp4' -> '/app/.config/QQ/NapCat/temp/4a811d3d-0a39-4725-bf8b-986234dd838c.mp4'
```

这是因为Yunzai在Windows上生成的绝对路径无法被Docker容器中的QQ客户端访问。

## 解决方案
路径转换器插件已经为您创建并配置好了！

## 配置步骤

### 1. 确认插件位置
插件文件位于：`plugins/system/path-converter.js`

### 2. 检查路径映射配置
打开插件文件，确认路径映射是否正确：

```javascript
this.pathMapping = {
  // Windows路径前缀 -> Docker容器路径前缀
  "C:\\Users\\<USER>\\anyulapp\\Yunzai": "/root/Yunzai"
}
```

### 3. 确认Docker映射
确保您的Docker容器启动时包含正确的路径映射：

```bash
docker run -v C:\Users\<USER>\anyulapp\Yunzai:/root/Yunzai ...
```

### 4. 重启Yunzai
重启Yunzai机器人以加载插件：

```bash
# 停止Yunzai
Ctrl+C

# 重新启动
node app.js
```

### 5. 验证插件工作
启动后您应该看到以下日志：
```
[INFO] [路径转换器] 插件加载完成，已启用Windows到Docker路径转换
```

当发送文件时，您会看到路径转换日志：
```
[DEBUG] [路径转换] C:\Users\<USER>\anyulapp\Yunzai\data\rcmp4\881042075\temp.mp4 -> /root/Yunzai/data/rcmp4/881042075/temp.mp4
```

## 常见问题

### Q: 插件没有生效怎么办？
A: 
1. 确认插件文件在正确位置
2. 检查路径映射配置是否匹配您的实际路径
3. 重启Yunzai
4. 查看启动日志确认插件加载

### Q: 路径转换了但文件还是无法上传？
A: 
1. 确认Docker容器的路径映射是否正确
2. 检查容器内文件权限
3. 验证文件在容器内是否真实存在

### Q: 如何添加多个路径映射？
A: 在pathMapping中添加更多映射规则：
```javascript
this.pathMapping = {
  "C:\\Users\\<USER>\\anyulapp\\Yunzai": "/root/Yunzai",
  "D:\\MyBot\\Yunzai": "/app/Yunzai",
  "E:\\Backup\\Yunzai": "/backup/Yunzai"
}
```

### Q: 插件会影响性能吗？
A: 不会。插件只在检测到Windows绝对路径时才进行转换，对其他路径类型没有影响。

## 测试方法

发送一个包含文件的消息，观察日志输出：

1. 成功的情况：
```
[DEBUG] [路径转换] C:\Users\<USER>\anyulapp\Yunzai\data\file.mp4 -> /root/Yunzai/data/file.mp4
[INFO] 发送群消息成功
```

2. 失败的情况（需要检查配置）：
```
[ERROR] 发送消息错误 ENOENT: no such file or directory
```

## 支持的文件类型
- 图片：jpg, png, gif, webp等
- 视频：mp4, avi, mov等  
- 音频：mp3, wav, ogg等
- 文档：pdf, txt, doc等

## 技术支持
如果遇到问题，请检查：
1. Yunzai启动日志
2. 路径映射配置
3. Docker容器配置
4. 文件权限设置

插件会自动处理所有文件上传场景，无需手动干预。
