# Yunzai路径转换器使用说明

## 问题背景

当Yunzai机器人运行在Windows系统上，而QQ客户端运行在Docker容器中时，会出现文件路径不匹配的问题。Yunzai生成的是Windows绝对路径（如`C:\Users\<USER>\anyulapp\Yunzai\data\rcmp4\881042075\temp.mp4`），但Docker容器中的QQ客户端无法访问这些Windows路径，导致文件上传失败。

## 解决方案

路径转换器插件通过拦截所有文件处理相关的方法，将Windows路径自动转换为Docker容器内的对应路径。

## 插件功能

### 1. 路径映射配置
```javascript
this.pathMapping = {
  // Windows路径前缀 -> Docker容器路径前缀
  "C:\\Users\\<USER>\\anyulapp\\Yunzai": "/root/Yunzai"
}
```

### 2. 拦截的方法
- `Bot.Buffer()` - 核心文件处理方法
- `Bot.fileType()` - 文件类型检测方法
- `Bot.fileToUrl()` - 文件转URL方法
- 各适配器的`makeFile()`方法
- `Bot.sendGroupFile()` - 群文件上传
- `Bot.sendFriendFile()` - 好友文件上传

### 3. 支持的路径格式
- Windows绝对路径：`C:\Users\<USER>\anyulapp\Yunzai\data\file.mp4`
- file://协议路径：`file://C:\Users\<USER>\anyulapp\Yunzai\data\file.mp4`
- 自动识别并转换为：`/root/Yunzai/data/file.mp4`

## 安装和配置

### 1. 插件位置
插件文件位于：`plugins/system/path-converter.js`

### 2. 配置路径映射
根据您的实际情况修改`pathMapping`配置：

```javascript
this.pathMapping = {
  // 将您的Windows Yunzai路径映射到Docker容器路径
  "C:\\Users\\<USER>\\anyulapp\\Yunzai": "/root/Yunzai",
  // 可以添加多个映射规则
  "D:\\MyBot\\Yunzai": "/app/Yunzai"
}
```

### 3. 启动插件
插件会在Yunzai启动时自动加载，无需手动启用。

## 工作原理

### 1. 路径检测
插件使用正则表达式检测Windows绝对路径：
```javascript
isWindowsAbsolutePath(filePath) {
  return /^[A-Za-z]:\\/.test(filePath)
}
```

### 2. 路径转换
将Windows路径的反斜杠转换为正斜杠，然后根据映射规则替换路径前缀：
```javascript
convertPath(windowsPath) {
  let normalizedPath = windowsPath.replace(/\\/g, '/')
  // 查找匹配的映射规则并替换
}
```

### 3. 方法拦截
通过重写原始方法，在文件处理前进行路径转换：
```javascript
const originalBuffer = Bot.Buffer
Bot.Buffer = async function(data, opts = {}) {
  // 路径转换逻辑
  return originalBuffer.call(this, data, opts)
}
```

## 日志输出

插件会输出详细的调试日志，帮助您了解路径转换过程：

```
[16:43:44.122][DEBUG] [路径转换] C:\Users\<USER>\anyulapp\Yunzai\data\rcmp4\881042075\temp.mp4 -> /root/Yunzai/data/rcmp4/881042075/temp.mp4
[16:43:44.123][INFO] [路径转换器] 插件加载完成，已启用Windows到Docker路径转换
```

## 故障排除

### 1. 路径转换不生效
- 检查`pathMapping`配置是否正确
- 确认Windows路径前缀完全匹配
- 查看日志输出确认插件是否正常加载

### 2. 文件仍然无法上传
- 确认Docker容器中的路径映射是否正确
- 检查文件权限是否正确
- 验证容器内文件是否真实存在

### 3. 性能影响
插件只在检测到Windows绝对路径时才进行转换，对其他路径类型（相对路径、HTTP URL等）不会产生性能影响。

## 兼容性

- 支持所有Yunzai适配器（OneBotv11、OPQBot、ComWeChat、GSUIDCore、Satori）
- 兼容各种文件类型（图片、视频、音频、文档等）
- 支持file://协议和普通文件路径

## 注意事项

1. 确保Docker容器的路径映射与插件配置一致
2. 插件会在Yunzai启动时自动加载，无需手动干预
3. 路径转换只影响文件上传，不会修改本地文件系统
4. 建议定期检查日志确认插件正常工作

## 更新日志

- v1.0.0: 初始版本，支持基本的Windows到Docker路径转换
- v1.1.0: 增加file://协议支持，完善错误处理
- v1.2.0: 优化性能，增加详细日志输出
